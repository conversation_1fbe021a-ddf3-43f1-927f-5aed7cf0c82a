#[cfg(test)]
mod tests;

use std::os::linux::net::SocketAddrExt;
use std::os::unix::net::{SocketAddr, UnixDatagram};
use std::sync::Arc;
use std::{env, time::Duration};
use tokio::sync::RwLock;
use tokio::time::Instant;
use tracing::{debug, error, info, warn};

const NOTIFY_SOCKET_NAME: &str = "NOTIFY_SOCKET";
const READY_MESSAGE: &str = "READY=1";
const WATCHDOG_MESSAGE: &str = "WATCHDOG=1";

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct TaskId(pub String);

impl TaskId {
    pub fn new(name: &str) -> Self {
        Self(name.to_string())
    }
}
#[derive(Debug, Clone)]
pub struct TaskHealth {
    pub last_heartbeat: Instant,
    pub timeout: Duration,
}

impl TaskHealth {
    pub fn new(timeout: Duration) -> Self {
        Self {
            last_heartbeat: Instant::now(),
            timeout,
        }
    }

    pub fn is_healthy(&self) -> bool {
        self.last_heartbeat.elapsed() < self.timeout
    }

    pub fn update_heartbeat(&mut self) {
        self.last_heartbeat = Instant::now();
    }
}

#[derive(Debug, Clone)]
pub struct TaskHealthMonitor {
    tasks: Arc<RwLock<std::collections::HashMap<TaskId, TaskHealth>>>,
}

impl TaskHealthMonitor {
    pub fn new() -> Self {
        Self {
            tasks: Arc::new(RwLock::new(std::collections::HashMap::new())),
        }
    }

    pub async fn register_task(&self, task_id: TaskId, timeout: Duration) {
        let mut tasks = self.tasks.write().await;
        tasks.insert(task_id.clone(), TaskHealth::new(timeout));
        info!(
            "Tâche '{}' enregistrée pour surveillance de santé (timeout: {:?})",
            task_id.0, timeout
        );
    }

    pub async fn heartbeat(&self, task_id: &TaskId) {
        let mut tasks = self.tasks.write().await;
        if let Some(task_health) = tasks.get_mut(task_id) {
            task_health.update_heartbeat();
            debug!("Heartbeat mis à jour pour la tâche '{}'", task_id.0);
        } else {
            warn!(
                "Tentative de mise à jour du heartbeat pour une tâche non enregistrée '{}'",
                task_id.0
            );
        }
    }

    pub async fn get_health_status(&self) -> Vec<(TaskId, bool)> {
        let tasks = self.tasks.read().await;
        tasks
            .iter()
            .map(|(id, health)| (id.clone(), health.is_healthy()))
            .collect()
    }

    pub async fn unregister_task(&self, task_id: &TaskId) {
        let mut tasks = self.tasks.write().await;
        if tasks.remove(task_id).is_some() {
            info!(
                "Tâche '{}' désenregistrée de la surveillance de santé",
                task_id.0
            );
        }
    }
}

#[derive(Debug)]
pub struct SystemdWatchdog {
    interval: Duration,
    health_monitor: Option<TaskHealthMonitor>,
    socket: UnixDatagram,
    socket_path: String,
}

impl SystemdWatchdog {
    /// Crée un nouveau SystemdWatchdog depuis l'environnement
    /// Retourne None si la variable d'environnement NOTIFY_SOCKET n'est pas présente
    pub fn from_env(interval: Duration) -> Option<Self> {
        let socket_path = match env::var(NOTIFY_SOCKET_NAME) {
            Ok(path) => {
                debug!("Socket de notification systemd trouvé à : {}", path);
                path
            }
            Err(_) => {
                debug!(
                    "Socket de notification systemd non disponible ({} non défini)",
                    NOTIFY_SOCKET_NAME
                );
                return None;
            }
        };

        let socket = match UnixDatagram::unbound() {
            Ok(socket) => socket,
            Err(e) => {
                error!("Échec de création du socket Unix datagram : {}", e);
                return None;
            }
        };

        info!("Initialisation du watchdog systemd avec configuration par défaut");
        Some(Self {
            interval,
            health_monitor: None,
            socket,
            socket_path,
        })
    }

    /// Crée un nouveau SystemdWatchdog avec monitoring de santé depuis l'environnement
    /// Retourne None si la variable d'environnement NOTIFY_SOCKET n'est pas présente
    pub fn from_env_with_health_monitor(
        interval: Duration,
        health_monitor: TaskHealthMonitor,
    ) -> Option<Self> {
        let socket_path = match env::var(NOTIFY_SOCKET_NAME) {
            Ok(path) => {
                debug!("Socket de notification systemd trouvé à : {}", path);
                path
            }
            Err(_) => {
                debug!(
                    "Socket de notification systemd non disponible ({} non défini)",
                    NOTIFY_SOCKET_NAME
                );
                return None;
            }
        };

        let socket = match UnixDatagram::unbound() {
            Ok(socket) => socket,
            Err(e) => {
                error!("Échec de création du socket Unix datagram : {}", e);
                return None;
            }
        };

        info!("Initialisation du watchdog systemd avec monitoring de santé activé");
        Some(Self {
            interval,
            health_monitor: Some(health_monitor),
            socket,
            socket_path,
        })
    }

    pub fn get_health_monitor(&self) -> Option<&TaskHealthMonitor> {
        self.health_monitor.as_ref()
    }

    pub async fn start(&self) {
        info!("Démarrage du service watchdog systemd");

        // Send initial ready signal
        match self.send_ready_signal() {
            Ok(()) => debug!("Signal de prêt envoyé avec succès à systemd"),
            Err(e) => warn!("Échec d'envoi du signal de prêt à systemd: {}", e),
        }

        // Start heartbeat loop
        loop {
            match self.send_heartbeat() {
                Ok(()) => debug!("Heartbeat watchdog envoyé avec succès"),
                Err(e) => warn!("Échec d'envoi du heartbeat watchdog: {}", e),
            }

            tokio::time::sleep(self.interval).await;
        }
    }

    fn send_heartbeat(&self) -> Result<(), std::io::Error> {
        self.send_notification(WATCHDOG_MESSAGE)
    }

    fn send_ready_signal(&self) -> Result<(), std::io::Error> {
        self.send_notification(READY_MESSAGE)
    }

    fn send_notification(&self, message: &str) -> Result<(), std::io::Error> {
        let message_bytes = message.as_bytes();

        // Send message to socket (handle both abstract and file-based sockets)
        let result = {
            if self.socket_path.starts_with('@') {
                let abstract_name = &self.socket_path[1..]; // Remove the '@' prefix
                let addr = SocketAddr::from_abstract_name(abstract_name.as_bytes())?;
                self.socket.connect_addr(&addr)?;
                self.socket.send(message_bytes)
            } else {
                self.socket.send_to(message_bytes, &self.socket_path)
            }
        };

        result.map_err(|e| {
            error!(
                "Échec d'envoi de notification au socket systemd '{}': {}",
                self.socket_path, e
            );
            e
        })?;

        debug!("Notification envoyée avec succès à systemd");
        Ok(())
    }
}
